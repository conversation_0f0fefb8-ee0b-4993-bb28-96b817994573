import { JobCandidate } from '@/api-requests/job-candidate/types';

interface IProps {
  candidate: JobCandidate;
}
// TODO: only show this tab when user applied with simulation
export default function OverviewTab({ candidate }: IProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        {(candidate.applyMode || 'simulation') === 'simulation' && (
          <div className="rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
            <div className="text-gray-500">Overall Score</div>
            <div className="text-sm text-gray-500">
              <span className="text-base font-bold text-primary">
                {candidate.scores || '-'}
              </span>
              /100
            </div>
          </div>
        )}
        <div className="rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
          <div className="text-gray-500">Match</div>
          <div className="font-bold">{candidate.matchPercentage || '-'}%</div>
        </div>
      </div>

      <div>
        <div>Simulation Result</div>
        <div className="rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
          <div className="space-y-8">
            {candidate.aiEvaluation?.strengths?.length && (
              <section>
                <h3 className="font-bold">Strengths:</h3>
                <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                  {candidate.aiEvaluation?.strengths.map((item, idx) => (
                    <li key={idx}>{item}</li>
                  ))}
                </ul>
              </section>
            )}
            {candidate.aiEvaluation?.areasForImprovement?.length && (
              <section>
                <h3 className="font-bold">Weaknesses:</h3>
                <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                  {candidate.aiEvaluation?.areasForImprovement.map(
                    (item, idx) => (
                      <li key={idx}>{item}</li>
                    )
                  )}
                </ul>
              </section>
            )}
            {candidate.aiEvaluation?.personalities?.length && (
              <section>
                <h3 className="font-bold">Personalities:</h3>
                <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                  {candidate.aiEvaluation?.personalities.map((item, idx) => (
                    <li key={idx}>
                      <span className="font-semibold">{item.name}:</span>{' '}
                      {item.explanation}
                    </li>
                  ))}
                </ul>
              </section>
            )}

            <section>
              <h3 className="font-bold">Skills Assessment:</h3>

              {(candidate.aiEvaluation?.skills?.hardSkills?.length ||
                candidate.aiEvaluation?.skills?.softSkills?.length) && (
                <div className="grid gap-8 md:grid-cols-[2fr_2fr_3fr] lg:grid-cols-[1.5fr_1.5fr_5fr]">
                  <div>
                    <div className="text-sm font-bold">Hard Skills:</div>
                    <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                      {(candidate.aiEvaluation?.skills?.hardSkills || [])?.map(
                        (item, idx) => (
                          <li key={idx}>
                            {item.name}:{' '}
                            <span className="font-bold">{item.rating}/5</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>

                  <div>
                    <h4 className="text-sm font-bold">Soft Skills:</h4>
                    <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                      {(candidate.aiEvaluation?.skills?.softSkills || [])?.map(
                        (item, idx) => (
                          <li key={idx}>
                            {item.name}:{' '}
                            <span className="font-bold">{item.rating}/5</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>

                  {/* <div>
                  <h4 className="text-sm font-bold">
                    Work Style & Communication
                  </h4>
                  <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                    <li>Communicates concisely with full context.</li>
                    <li>Proactive and manages time well.</li>
                    <li>Clean code style following conventions.</li>
                  </ul>
                </div> */}
                </div>
              )}
            </section>

            {candidate.tasks?.length && (
              <section>
                <h3 className="font-bold">Task Results:</h3>
                <ul className="mt-1 list-decimal space-y-2 pl-6 text-sm leading-relaxed">
                  {candidate.tasks.map((item, idx) => (
                    <li key={idx}>
                      <p className="font-semibold">{item.title}</p>
                      <p>Description: {item.description}</p>
                      <div>
                        <p>Submission:</p>
                        <p className="whitespace-pre-wrap rounded bg-gray-100 p-2">
                          {item.submission?.content}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              </section>
            )}

            {/* <section>
              <h3 className="font-bold">Strengths:</h3>
              <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                <li>
                  <span className="font-semibold">UI/UX & Responsive:</span>{' '}
                  Excellent; smooth mobile–tablet–desktop handling.
                </li>
                <li>
                  <span className="font-semibold">Debug & DevTools:</span>{' '}
                  Quickly identified and fixed bugs, especially in
                  Safari/Firefox.
                </li>
                <li>
                  <span className="font-semibold">JavaScript/React:</span> Clear
                  logic, well-structured components, clean naming.
                </li>
              </ul>
            </section> */}

            {/* <section>
              <h3 className="font-bold">Weaknesses:</h3>
              <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                <li>
                  <span className="font-semibold">Performance:</span> Did not
                  optimize bundle (no code-splitting, image optimization).
                </li>
                <li>
                  <span className="font-semibold">
                    Advanced State Management:
                  </span>{' '}
                  Some struggle with complex async data (caching, pagination).
                </li>
                <li>
                  <span className="font-semibold">Testing:</span> Missing basic
                  unit and component tests.
                </li>
              </ul>
            </section>

            <section>
              <h3 className="font-bold">Task Results:</h3>
              <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                <li>
                  <span className="font-semibold">Performance:</span> Did not
                  optimize bundle (no code-splitting, image optimization).
                </li>
                <li>
                  <span className="font-semibold">
                    Advanced State Management:
                  </span>{' '}
                  Some struggle with complex async data (caching, pagination).
                </li>
                <li>
                  <span className="font-semibold">Testing:</span> Missing basic
                  unit and component tests.
                </li>
              </ul>
            </section>

            <section>
              <h3 className="font-bold">Skills Assessment:</h3>

              <div className="grid gap-8 md:grid-cols-[2fr_2fr_3fr] lg:grid-cols-[1.5fr_1.5fr_5fr]">
                <div>
                  <div className="text-sm font-bold">Hard Skills:</div>
                  <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                    <li>
                      HTML5/CSS3: <span className="font-bold">4.5/5</span>
                    </li>
                    <li>
                      JavaScript ES6+: <span className="font-bold">4.2/5</span>
                    </li>
                    <li>
                      React (components, hooks):{' '}
                      <span className="font-bold">4.0/5</span>
                    </li>
                    <li>
                      API Integration (REST):{' '}
                      <span className="font-bold">3.8/5</span>
                    </li>
                    <li>
                      Responsive/Cross-browser:{' '}
                      <span className="font-bold">5.0/5</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="text-sm font-bold">Soft Skills</h4>
                  <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                    <li>
                      Problem Solving: <span className="font-bold">4.3/5</span>
                    </li>
                    <li>
                      Time Management: <span className="font-bold">4.0/5</span>
                    </li>
                    <li>
                      Attention to Detail:{' '}
                      <span className="font-bold">4.4/5</span>
                    </li>
                    <li>
                      Communication (written & verbal):{' '}
                      <span className="font-bold">3.8/5</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="text-sm font-bold">
                    Work Style & Communication
                  </h4>
                  <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                    <li>Communicates concisely with full context.</li>
                    <li>Proactive and manages time well.</li>
                    <li>Clean code style following conventions.</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h3 className="font-bold">Conclusion & Recommendations</h3>
              <ul className="mt-1 list-disc pl-6 text-sm leading-relaxed">
                <li>
                  <span className="font-semibold">UI/UX & Responsive:</span>{' '}
                  Excellent; smooth mobile–tablet–desktop handling.
                </li>
                <li>
                  <span className="font-semibold">Debug & DevTools:</span>{' '}
                  Quickly identified and fixed bugs, especially in
                  Safari/Firefox.
                </li>
                <li>
                  <span className="font-semibold">JavaScript/React:</span> Clear
                  logic, well-structured components, clean naming.
                </li>
              </ul>
            </section> */}
          </div>
        </div>
      </div>
    </div>
  );
}
